// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables:', {
    url: !!supabaseUrl,
    key: !!supabaseAnonKey
  })
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.')
}

// Only log in development
if (process.env.NODE_ENV === 'development') {
  console.log('Supabase initialization:', {
    url: supabaseUrl?.substring(0, 20) + '...',
    hasKey: !!supabaseAnonKey
  })
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'implicit'
  }
})

// Types for better TypeScript support
export type Database = {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      test_sessions: {
        Row: {
          id: string
          user_id: string
          session_id: string
          mode: 'test' | 'practice'
          max_items: number
          target_subjects: string[] | null
          show_solution: boolean
          time_limit: number | null
          start_time: string
          current_ability: number[]
          answered_items: string[]
          responses: Json[]
          recent_performance: number[]
          subject_question_count: Json
          status: 'active' | 'completed' | 'abandoned'
          completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          session_id: string
          mode: 'test' | 'practice'
          max_items: number
          target_subjects?: string[] | null
          show_solution: boolean
          time_limit?: number | null
          start_time: string
          current_ability: number[]
          answered_items?: string[]
          responses?: Json[]
          recent_performance?: number[]
          subject_question_count: Json
          status?: 'active' | 'completed' | 'abandoned'
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          session_id?: string
          mode?: 'test' | 'practice'
          max_items?: number
          target_subjects?: string[] | null
          show_solution?: boolean
          time_limit?: number | null
          start_time?: string
          current_ability?: number[]
          answered_items?: string[]
          responses?: Json[]
          recent_performance?: number[]
          subject_question_count?: Json
          status?: 'active' | 'completed' | 'abandoned'
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {}
    Functions: {}
    Enums: {}
    CompositeTypes: {}
  }
}

type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]