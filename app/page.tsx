// math-adaptive-test/app/page.tsx

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Brain,
  Target,
  TrendingUp,
  BookOpen,
  BarChart3,
  Users,
  Zap,
  Shield,
  ArrowRight,
  CheckCircle2,
  Star,
  Timer,
  Trophy,
  Sparkles,
  Calculator,
  GraduationCap,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { AuthSection } from '@/components/auth/auth-button';

// Subject data
const SUBJECTS = [
  { id: 'algebra', name: 'Algebra', icon: Calculator, color: 'bg-red-500' },
  { id: 'counting_and_probability', name: 'Counting & Probability', icon: BarChart3, color: 'bg-cyan-500' },
  { id: 'geometry', name: 'Geometry', icon: Target, color: 'bg-blue-500' },
  { id: 'intermediate_algebra', name: 'Intermediate Algebra', icon: TrendingUp, color: 'bg-green-500' },
  { id: 'number_theory', name: 'Number Theory', icon: Brain, color: 'bg-yellow-500' },
  { id: 'prealgebra', name: 'Pre-algebra', icon: BookOpen, color: 'bg-purple-500' }
];

// Feature cards data
const FEATURES = [
  {
    icon: Brain,
    title: 'Adaptive Testing',
    description: 'Questions automatically adjust to your skill level for optimal learning',
    color: 'text-blue-600'
  },
  {
    icon: BarChart3,
    title: 'Detailed Analytics',
    description: 'Track your progress with comprehensive performance insights',
    color: 'text-green-600'
  },
  {
    icon: Target,
    title: 'Personalized Learning',
    description: 'Get recommendations tailored to your strengths and weaknesses',
    color: 'text-purple-600'
  },
  {
    icon: Zap,
    title: 'Real-time Feedback',
    description: 'Instant feedback helps you learn from mistakes immediately',
    color: 'text-yellow-600'
  }
];

// Statistics
const STATS = [
  { label: 'Questions Available', value: '7,500+', icon: Calculator },
  { label: 'Skill Levels', value: '5', icon: TrendingUp },
  { label: 'Subject Areas', value: '6', icon: BookOpen },
  { label: 'Average Score Improvement', value: '23%', icon: Trophy }
];

export default function HomePage() {
  const router = useRouter();
  const [selectedMode, setSelectedMode] = useState<'test' | 'practice'>('test');
  const [selectedQuestions, setSelectedQuestions] = useState(20);
  const [timeLimit, setTimeLimit] = useState<number | null>(null);
  const [isSequentialMode, setIsSequentialMode] = useState(false);
  const [startQuestion, setStartQuestion] = useState(1);

  const handleStartTest = () => {
    const params = new URLSearchParams({
      mode: selectedMode
    });

    if (isSequentialMode) {
      params.append('question', startQuestion.toString());
    } else {
      params.append('questions', selectedQuestions.toString());
    }

    if (timeLimit) {
      params.append('timeLimit', timeLimit.toString());
    }

    router.push(`/test?${params.toString()}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
      {/* Navigation */}
      <nav className="relative z-10 container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Brain className="w-8 h-8 text-primary" />
            <span className="text-xl font-bold">Math Adaptive Test</span>
          </div>
          <AuthSection />
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-slate-100 [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]" />
        <div className="container mx-auto px-4 py-16 sm:py-24 relative">
          <div className="text-center max-w-4xl mx-auto">
            <Badge className="mb-4" variant="secondary">
              <Sparkles className="w-3 h-3 mr-1" />
              Powered by Advanced IRT Algorithms
            </Badge>
            
            <h1 className="text-4xl sm:text-6xl font-bold tracking-tight mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Master Mathematics with
              <br />
              Adaptive Intelligence
            </h1>
            
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Experience personalized math assessment that adapts to your skill level in real-time.
              Practice problems from algebra to number theory with instant feedback.
            </p>

            {/* Quick Start Card */}
            <Card className="max-w-2xl mx-auto shadow-lg">
              <CardHeader>
                <CardTitle>Start Your Assessment</CardTitle>
                <CardDescription>
                  Choose your mode and begin your personalized math journey
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Tabs value={selectedMode} onValueChange={(v) => setSelectedMode(v as 'test' | 'practice')}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="test">
                      <Shield className="w-4 h-4 mr-2" />
                      Formal Test
                    </TabsTrigger>
                    <TabsTrigger value="practice">
                      <BookOpen className="w-4 h-4 mr-2" />
                      Practice Mode
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="test" className="text-left mt-4">
                    <div className="space-y-2 text-sm">
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                        Comprehensive skill assessment
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                        No solutions shown during test
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                        Detailed report upon completion
                      </p>
                    </div>
                  </TabsContent>
                  <TabsContent value="practice" className="text-left mt-4">
                    <div className="space-y-2 text-sm">
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                        Learn at your own pace
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                        View solutions and explanations
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                        Adaptive difficulty adjustment
                      </p>
                    </div>
                  </TabsContent>
                </Tabs>

                {/* Question Selection Mode */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Question Selection</span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant={!isSequentialMode ? "default" : "outline"}
                      size="sm"
                      onClick={() => setIsSequentialMode(false)}
                      className="flex-1"
                    >
                      Adaptive
                    </Button>
                    <Button
                      variant={isSequentialMode ? "default" : "outline"}
                      size="sm"
                      onClick={() => setIsSequentialMode(true)}
                      className="flex-1"
                    >
                      Sequential
                    </Button>
                  </div>
                </div>

                {/* Question Configuration */}
                {!isSequentialMode ? (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Number of Questions</span>
                      <span className="text-sm text-muted-foreground">{selectedQuestions}</span>
                    </div>
                    <input
                      type="range"
                      min="10"
                      max="50"
                      step="5"
                      value={selectedQuestions}
                      onChange={(e) => setSelectedQuestions(Number(e.target.value))}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>10</span>
                      <span>30</span>
                      <span>50</span>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Start from Question</span>
                      <span className="text-sm text-muted-foreground">#{startQuestion}</span>
                    </div>
                    <input
                      type="number"
                      min="1"
                      max="10000"
                      value={startQuestion}
                      onChange={(e) => setStartQuestion(Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Enter question number"
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium flex items-center gap-2">
                      <Timer className="w-4 h-4" />
                      Time Limit
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {timeLimit ? `${timeLimit} min` : 'No limit'}
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant={timeLimit === null ? "default" : "outline"}
                      size="sm"
                      onClick={() => setTimeLimit(null)}
                      className="flex-1"
                    >
                      No Limit
                    </Button>
                    <Button
                      variant={timeLimit === 1 ? "default" : "outline"}
                      size="sm"
                      onClick={() => setTimeLimit(1)}
                      className="flex-1"
                    >
                      1 min (Test)
                    </Button>
                    <Button
                      variant={timeLimit === 60 ? "default" : "outline"}
                      size="sm"
                      onClick={() => setTimeLimit(60)}
                      className="flex-1"
                    >
                      60 min
                    </Button>
                    <Button
                      variant={timeLimit === 90 ? "default" : "outline"}
                      size="sm"
                      onClick={() => setTimeLimit(90)}
                      className="flex-1"
                    >
                      90 min
                    </Button>
                  </div>
                </div>

                <Button 
                  size="lg" 
                  className="w-full"
                  onClick={handleStartTest}
                >
                  Start {selectedMode === 'test' ? 'Test' : 'Practice'}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-secondary/30">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {STATS.map((stat) => (
              <div key={stat.label} className="text-center">
                <stat.icon className="w-8 h-8 mx-auto mb-2 text-primary" />
                <div className="text-3xl font-bold">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose Our Platform?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our adaptive testing engine uses advanced Item Response Theory to provide
              the most effective learning experience
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {FEATURES.map((feature) => (
              <Card key={feature.title} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <feature.icon className={cn("w-10 h-10 mb-2", feature.color)} />
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Subjects Section */}
      <section className="py-16 bg-secondary/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Comprehensive Coverage</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Master all areas of mathematics with our extensive question bank
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
            {SUBJECTS.map((subject) => (
              <Card key={subject.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
                <CardHeader className="flex flex-row items-center space-x-4">
                  <div className={cn("p-3 rounded-lg", subject.color)}>
                    <subject.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center justify-between">
                      {subject.name}
                      <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:translate-x-1 transition-transform" />
                    </CardTitle>
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">How It Works</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Get started in three simple steps
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {[
              {
                step: '1',
                title: 'Choose Your Mode',
                description: 'Select between formal test or practice mode based on your goals',
                icon: Target
              },
              {
                step: '2',
                title: 'Answer Questions',
                description: 'Work through adaptive questions that match your skill level',
                icon: Brain
              },
              {
                step: '3',
                title: 'Get Results',
                description: 'Receive detailed analytics and personalized recommendations',
                icon: Trophy
              }
            ].map((item) => (
              <div key={item.step} className="text-center">
                <div className="relative">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <item.icon className="w-8 h-8 text-primary" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">
                    {item.step}
                  </div>
                </div>
                <h3 className="font-semibold mb-2">{item.title}</h3>
                <p className="text-sm text-muted-foreground">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary/5">
        <div className="container mx-auto px-4 text-center">
          <GraduationCap className="w-16 h-16 mx-auto mb-4 text-primary" />
          <h2 className="text-3xl font-bold mb-4">Ready to Excel in Mathematics?</h2>
          <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join thousands of students improving their math skills with our adaptive platform
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" onClick={() => handleStartTest()}>
              Start Learning Now
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
            <Button size="lg" variant="outline" onClick={() => router.push('/formula-check')}>
              Check Formula Rendering
            </Button>
            <Button size="lg" variant="outline" onClick={() => router.push('/about')}>
              Learn More
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2">
              <Brain className="w-6 h-6" />
              <span className="font-semibold">Math Adaptive Test</span>
            </div>
            <div className="flex gap-6 text-sm text-muted-foreground">
              <a href="/formula-check" className="hover:text-foreground transition-colors">Formula Check</a>
              <a href="/privacy" className="hover:text-foreground transition-colors">Privacy</a>
              <a href="/terms" className="hover:text-foreground transition-colors">Terms</a>
              <a href="/about" className="hover:text-foreground transition-colors">About</a>
              <a href="/contact" className="hover:text-foreground transition-colors">Contact</a>
            </div>
            <div className="text-sm text-muted-foreground">
              © 2024 Math Adaptive Test. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
