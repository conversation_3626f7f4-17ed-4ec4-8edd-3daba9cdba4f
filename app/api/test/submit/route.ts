// math-adaptive-test/app/api/test/submit/route.ts

import { NextRequest, NextResponse } from 'next/server';
// ✅ 关键改动: 从共享的 session-store 导入
import { getSessionById, setSession, TestResponse } from '../session-store';
import { PythonShell } from 'python-shell'; // ✅ 1. 导入 PythonShell
// 从 data-loader 导入所有需要的函数
import { getProblemById, loadProblems, initializeItemParams } from '@/lib/data-loader';

// ... (keep the rest of the file the same until the POST function)

// API Route Handler for submitting answers
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { sessionId, itemId, userAnswer, timeSpent } = body;

    if (!sessionId || !itemId) {
      return NextResponse.json({ error: 'Session ID and Item ID are required' }, { status: 400 });
    }

    // ✅ 关键改动: 使用共享的 getSessionById 函数
    const session = await getSessionById(sessionId);
    if (!session) {
      // 这是你遇到错误的地方
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    const problem = await getProblemById(itemId);
    if (!problem) {
      return NextResponse.json({ error: 'Problem data not found' }, { status: 404 });
    }
    
    const correctAnswer = problem.correctAnswer || '';

    // 1. 答案检查 (不变)
    const answerCheckOptions = {
      mode: 'json' as const,
      pythonPath: process.env.PYTHON_PATH || 'python3',
      scriptPath: './python',
      args: [userAnswer || '', correctAnswer]
    };
    const shellResult = await PythonShell.run('answer_checker.py', answerCheckOptions);
    const isCorrect = shellResult[0].correct;

    // 2. 准备调用Python MLE脚本所需的数据
    const currentResponse: TestResponse = {
      itemId,
      subject: problem.subject,
      difficulty: problem.difficulty,
      correct: isCorrect,
      userAnswer: userAnswer || '',
      timestamp: new Date().toISOString(),
      timeSpent: timeSpent || 0
    };
    
    // 包含本次回答的完整历史记录
    const updatedResponses = [...session.responses, currentResponse];

    // 加载所有题目并初始化参数，这是Python脚本需要的
    const allProblems = await loadProblems();
    const allItemParams = initializeItemParams(allProblems);
    const itemParamsObject = Object.fromEntries(allItemParams.entries());

    const abilityUpdateArgs = {
        current_ability: session.currentAbility,
        responses: updatedResponses,
        item_params: itemParamsObject,
    };

    // 3. 调用 ability_updater.py 脚本 - 使用stdin避免参数过长
    const abilityResult = await new Promise((resolve, reject) => {
      const pyshell = new PythonShell('ability_updater.py', {
        mode: 'json' as const,
        pythonPath: process.env.PYTHON_PATH || 'python3',
        scriptPath: './python',
      });

      // 发送数据到stdin
      pyshell.send(abilityUpdateArgs);
      pyshell.end((err: any, exitCode: number, exitSignal: string) => {
        if (err) reject(err);
      });

      const results: any[] = [];
      pyshell.on('message', (message) => {
        results.push(message);
      });

      pyshell.on('error', (err) => {
        reject(err);
      });

      pyshell.on('close', () => {
        resolve(results);
      });
    });
    const newAbility = (abilityResult as any[])[0]?.updated_ability || session.currentAbility;

    // 4. 使用Python返回的精确结果更新会话
    session.responses.push(currentResponse);
    session.answeredItems.push(itemId);
    session.recentPerformance.push(isCorrect ? 1 : 0);
    if (session.recentPerformance.length > 10) {
      session.recentPerformance.shift(); // Keep only the last 10
    }
    if (problem.subject in session.subjectQuestionCount) {
      session.subjectQuestionCount[problem.subject]++;
    }
    
    // 🔥 使用MLE计算出的新能力值
    session.currentAbility = newAbility;

    if (session.responses.length >= session.maxItems) {
      session.status = 'completed';
      session.completedAt = new Date().toISOString();
    }

    // ✅ 关键改动: 使用共享的 setSession 函数保存更新
    setSession(sessionId, session);

    // ✅ 3. 更新返回给前端的结果
    const result = {
      correct: isCorrect,
      feedback: isCorrect ? 'Correct!' : 'Incorrect',
      //correctAnswer: isCorrect ? userAnswer : 'Sample correct answer',
      //solution: session.showSolution ? 'Sample solution explanation' : undefined,
      correctAnswer: session.showSolution ? correctAnswer : undefined,
      solution: session.showSolution ? problem.solution : undefined,
      progress: {
        completed: session.responses.length,
        total: session.maxItems,
        correctCount: session.responses.filter(r => r.correct).length,
        accuracy: Math.round((session.responses.filter(r => r.correct).length / session.responses.length) * 100) || 0,
      },
      updatedAbility: session.currentAbility,
    };

    return NextResponse.json(result);

  } catch (error: any) {
    console.error('Error in /api/test/submit:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}
