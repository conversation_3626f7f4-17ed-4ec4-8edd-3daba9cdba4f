// math-adaptive-test/app/api/test/start/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { TestSession, getSessionById, setSession } from '../session-store';

// Types
interface TestConfig {
  mode: 'test' | 'practice';          // Test mode
  maxItems: number;                   // Maximum number of questions
  targetSubjects?: string[];          // Target subjects (optional)
  showSolution: boolean;              // Whether to show solutions (practice mode)
  timeLimit?: number;                 // Time limit in minutes (optional)
}

// Constants
const SUBJECTS = [
  'algebra',
  'counting_and_probability', 
  'geometry',
  'intermediate_algebra',
  'number_theory',
  'prealgebra'
];

const DEFAULT_CONFIG: Partial<TestConfig> = {
  mode: 'test',
  maxItems: 20,
  showSolution: false,
  timeLimit: undefined  // Changed from null to undefined
};

// Validate test configuration
function validateConfig(config: any): { valid: boolean; error?: string } {
  if (!config || typeof config !== 'object') {
    return { valid: false, error: 'Configuration must be provided' };
  }

  // Validate mode
  if (config.mode && !['test', 'practice'].includes(config.mode)) {
    return { valid: false, error: 'Mode must be either "test" or "practice"' };
  }

  // Validate maxItems
  if (config.maxItems !== undefined) {
    const maxItems = Number(config.maxItems);
    if (isNaN(maxItems) || maxItems < 1 || maxItems > 100) {
      return { valid: false, error: 'maxItems must be between 1 and 100' };
    }
  }

  // Validate targetSubjects
  if (config.targetSubjects) {
    if (!Array.isArray(config.targetSubjects)) {
      return { valid: false, error: 'targetSubjects must be an array' };
    }
    
    for (const subject of config.targetSubjects) {
      if (!SUBJECTS.includes(subject)) {
        return { valid: false, error: `Invalid subject: ${subject}` };
      }
    }
  }

  // Validate timeLimit
  if (config.timeLimit !== undefined && config.timeLimit !== null) {
    const timeLimit = Number(config.timeLimit);
    if (isNaN(timeLimit) || timeLimit < 1 || timeLimit > 240) {
      return { valid: false, error: 'timeLimit must be between 1 and 240 minutes' };
    }
  }

  return { valid: true };
}

// API Route Handler
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    let config: Partial<TestConfig>;
    let userId: string | undefined;
    
    try {
      const body = await request.json();
      config = body.config || body;
      userId = body.userId;
    } catch {
      config = {};
    }

    // Merge with defaults
    const finalConfig: TestConfig = {
      ...DEFAULT_CONFIG,
      ...config,
      showSolution: config.mode === 'practice' ? 
        (config.showSolution !== false) : // Default true for practice
        false  // Always false for test mode
    } as TestConfig;

    // Validate configuration
    const validation = validateConfig(finalConfig);
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    // Extract user ID from headers if not in body
    userId = userId || request.headers.get('x-user-id') || undefined;

    // Create new session
    const sessionId = uuidv4();
    const session: TestSession = {
      sessionId,
      userId,
      mode: finalConfig.mode,
      maxItems: finalConfig.maxItems,
      targetSubjects: finalConfig.targetSubjects || undefined,  // Changed from null to undefined
      showSolution: finalConfig.showSolution,
      timeLimit: finalConfig.timeLimit || undefined,  // Changed from null to undefined
      startTime: new Date().toISOString(),
      currentAbility: [0, 0, 0, 0, 0, 0], // Initialize with zeros
      answeredItems: [],
      responses: [],
      recentPerformance: [],
      subjectQuestionCount: Object.fromEntries(SUBJECTS.map(s => [s, 0])),
      status: 'active'
    };

    // Store session
    await setSession(sessionId, session);

    // Prepare response
    const response = {
      sessionId: session.sessionId,
      config: {
        mode: session.mode,
        maxItems: session.maxItems,
        targetSubjects: session.targetSubjects,
        showSolution: session.showSolution,
        timeLimit: session.timeLimit
      },
      session: {
        startTime: session.startTime,
        currentAbility: session.currentAbility,
        subjectQuestionCount: session.subjectQuestionCount,
        status: session.status
      },
      metadata: {
        availableSubjects: SUBJECTS,
        totalQuestions: session.maxItems,
        estimatedDuration: session.timeLimit || Math.ceil(session.maxItems * 2.5), // Estimate 2.5 min per question
        instructions: getInstructions(session.mode)
      }
    };

    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    console.error('Error in /api/test/start:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve session information
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const session = await getSessionById(sessionId);
    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Calculate progress
    const progress = {
      completed: session.responses.length,
      total: session.maxItems,
      percentage: Math.round((session.responses.length / session.maxItems) * 100),
      correctCount: session.responses.filter(r => r.correct).length,
      accuracy: session.responses.length > 0 ? 
        Math.round((session.responses.filter(r => r.correct).length / session.responses.length) * 100) : 0
    };

    // Calculate time elapsed
    const elapsed = Date.now() - new Date(session.startTime).getTime();
    const elapsedMinutes = Math.floor(elapsed / 60000);
    const remainingTime = session.timeLimit ? 
      Math.max(0, session.timeLimit - elapsedMinutes) : undefined;  // Changed from null to undefined

    // Subject-wise statistics
    const subjectStats: Record<string, { total: number; correct: number; accuracy: number }> = {};
    for (const response of session.responses) {
      if (!subjectStats[response.subject]) {
        subjectStats[response.subject] = { total: 0, correct: 0, accuracy: 0 };
      }
      subjectStats[response.subject].total++;
      if (response.correct) {
        subjectStats[response.subject].correct++;
      }
    }

    // Calculate accuracy for each subject
    for (const subject in subjectStats) {
      const stats = subjectStats[subject];
      stats.accuracy = stats.total > 0 ? 
        Math.round((stats.correct / stats.total) * 100) : 0;
    }

    const response = {
      sessionId: session.sessionId,
      status: session.status,
      config: {
        mode: session.mode,
        maxItems: session.maxItems,
        targetSubjects: session.targetSubjects,
        showSolution: session.showSolution,
        timeLimit: session.timeLimit
      },
      progress,
      timing: {
        startTime: session.startTime,
        elapsedMinutes,
        remainingTime,
        isExpired: session.timeLimit ? elapsedMinutes >= session.timeLimit : false
      },
      performance: {
        currentAbility: session.currentAbility,
        recentPerformance: session.recentPerformance.slice(-5), // Last 5 responses
        subjectStats,
        subjectQuestionCount: session.subjectQuestionCount
      },
      data: {
        answeredItems: session.answeredItems,
        responses: session.responses
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in /api/test/start GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to get instructions based on mode
function getInstructions(mode: 'test' | 'practice'): string[] {
  const commonInstructions = [
    'Read each question carefully before answering',
    'Fractions can be entered as 1/2 or \\frac{1}{2}',
    'You can skip questions if needed',
    'Progress is saved automatically'
  ];

  if (mode === 'practice') {
    return [
      ...commonInstructions,
      'Solutions are available for each question',
      'The system will adapt difficulty based on your performance',
      'Questions are balanced across all subjects',
      'Perfect for learning and improving your skills'
    ];
  } else {
    return [
      ...commonInstructions,
      'This is a formal assessment - solutions are not shown',
      'Questions are selected to best measure your ability',
      'Try to answer as accurately as possible',
      'Your final score will be based on all responses'
    ];
  }
}

// Note: Session management functions are now imported from session-store.ts
