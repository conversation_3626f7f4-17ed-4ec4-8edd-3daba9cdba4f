// math-adaptive-test/app/api/test/session-store.ts

import { supabase } from '@/lib/supabase';

// Types
export interface TestSession {
  sessionId: string;                  // Unique session identifier
  userId?: string;                    // User ID (optional)
  mode: 'test' | 'practice';
  maxItems: number;
  targetSubjects: string[] | undefined;
  showSolution: boolean;
  timeLimit: number | undefined;
  startTime: string;                  // ISO timestamp
  currentAbility: number[];           // 6-dimensional ability array
  answeredItems: string[];            // List of answered item IDs
  responses: TestResponse[];          // All responses
  recentPerformance: number[];        // Recent performance (1s and 0s)
  subjectQuestionCount: Record<string, number>;  // Question count by subject
  status: 'active' | 'completed' | 'abandoned';
  completedAt?: string;               // ISO timestamp when completed
}

export interface TestResponse {
  itemId: string;
  subject: string;
  difficulty: number;
  correct: boolean;
  userAnswer: string;
  timestamp: string;
  timeSpent: number;                  // Time spent in seconds
}

// In-memory session storage (shared across all routes)
// This is just for demonstration - in production, use a database

// Use global singleton to ensure sessions persist across API route modules
function getGlobalSessions(): Map<string, TestSession> {
  if (typeof globalThis !== 'undefined') {
    if (!(globalThis as any).__sessions) {
      console.log('Creating new global sessions map');
      (globalThis as any).__sessions = new Map<string, TestSession>();
    }
    return (globalThis as any).__sessions;
  }
  // Fallback for environments without globalThis
  return new Map<string, TestSession>();
}

const sessions = getGlobalSessions();

// Debug: Log when the module is loaded
console.log('Session store module loaded, sessions map size:', sessions.size);

// Clean up old sessions periodically
setInterval(() => {
  const now = new Date();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  
  for (const [id, session] of sessions.entries()) {
    const sessionAge = now.getTime() - new Date(session.startTime).getTime();
    if (sessionAge > maxAge) {
      sessions.delete(id);
    }
  }
}, 60 * 60 * 1000); // Run every hour

// Session management functions (with Supabase integration)
export async function getSessionById(id: string): Promise<TestSession | undefined> {
  console.log('getSessionById called with:', JSON.stringify(id));

  // First try in-memory cache
  const cachedSession = sessions.get(id);
  if (cachedSession) {
    console.log('Found session in cache:', !!cachedSession);
    return cachedSession;
  }

  // Try to load from Supabase if not in cache
  try {
    const { data, error } = await supabase
      .from('test_sessions')
      .select('*')
      .eq('session_id', id)
      .single();

    if (error) {
      console.log('Session not found in database:', error.message);
      return undefined;
    }

    if (data) {
      // Convert database format to TestSession format
      const session: TestSession = {
        sessionId: data.session_id,
        userId: data.user_id,
        mode: data.mode,
        maxItems: data.max_items,
        targetSubjects: data.target_subjects,
        showSolution: data.show_solution,
        timeLimit: data.time_limit,
        startTime: data.start_time,
        currentAbility: data.current_ability,
        answeredItems: data.answered_items,
        responses: data.responses as TestResponse[],
        recentPerformance: data.recent_performance,
        subjectQuestionCount: data.subject_question_count as Record<string, number>,
        status: data.status,
        completedAt: data.completed_at
      };

      // Cache it for future use
      sessions.set(id, session);
      console.log('Loaded session from database and cached');
      return session;
    }
  } catch (error) {
    console.error('Error loading session from database:', error);
  }

  return undefined;
}

export async function setSession(id: string, session: TestSession): Promise<void> {
  console.log('setSession called with id:', JSON.stringify(id));

  // Store in memory cache
  sessions.set(id, session);
  console.log('Session stored in cache. Total sessions:', sessions.size);

  // Store in Supabase database if user is authenticated
  if (session.userId) {
    try {
      console.log('Saving session to database for user:', session.userId, 'session:', session.sessionId);

      // First try to get existing record to preserve created_at
      const { data: existingRecord } = await supabase
        .from('test_sessions')
        .select('created_at')
        .eq('session_id', session.sessionId)
        .eq('user_id', session.userId)
        .single();

      console.log('Existing record found:', !!existingRecord);
      const now = new Date().toISOString();
      
      const { error } = await supabase
        .from('test_sessions')
        .upsert({
          id: `${session.userId}_${session.sessionId}`, // Composite key
          user_id: session.userId,
          session_id: session.sessionId,
          mode: session.mode,
          max_items: session.maxItems,
          target_subjects: session.targetSubjects,
          show_solution: session.showSolution,
          time_limit: session.timeLimit,
          start_time: session.startTime,
          current_ability: session.currentAbility,
          answered_items: session.answeredItems,
          responses: session.responses as any[],
          recent_performance: session.recentPerformance,
          subject_question_count: session.subjectQuestionCount as any,
          status: session.status,
          completed_at: session.completedAt,
          created_at: existingRecord?.created_at || now, // Preserve original created_at or use current time for new records
          updated_at: now
        }, {
          onConflict: 'user_id,session_id'
        });

      if (error) {
        console.error('Error saving session to database:', error);
      } else {
        console.log('Session saved to database successfully');
      }
    } catch (error) {
      console.error('Unexpected error saving session:', error);
    }
  }
}

export async function updateSessionById(id: string, updates: Partial<TestSession>): Promise<boolean> {
  const session = sessions.get(id);
  if (!session) {
    // Try to load from database first
    const dbSession = await getSessionById(id);
    if (!dbSession) return false;
    Object.assign(dbSession, updates);
    await setSession(id, dbSession);
    return true;
  }
  
  Object.assign(session, updates);
  await setSession(id, session);
  return true;
}

export function deleteSession(id: string): boolean {
  const deleted = sessions.delete(id);
  
  // Also delete from database if possible (best effort)
  supabase
    .from('test_sessions')
    .delete()
    .eq('session_id', id)
    .then(({ error }) => {
      if (error) {
        console.error('Error deleting session from database:', error);
      }
    });

  return deleted;
}

// Helper function to check if user has access to a session
export async function hasSessionAccess(sessionId: string, currentUserId?: string): Promise<boolean> {
  try {
    // First try to get session from cache
    let session = sessions.get(sessionId);
    
    // If not in cache, try to load from database
    if (!session) {
      session = await getSessionById(sessionId);
    }
    
    if (!session) {
      return false; // Session doesn't exist
    }
    
    // If session belongs to anonymous user (no userId), it's publicly accessible
    if (!session.userId) {
      return true;
    }
    
    // If session has a userId, only that user can access it
    return session.userId === currentUserId;
  } catch (error) {
    console.error('Error checking session access:', error);
    return false;
  }
}

// Helper function to get user sessions
export async function getUserSessions(userId: string): Promise<TestSession[]> {
  try {
    const { data, error } = await supabase
      .from('test_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user sessions:', error);
      return [];
    }

    return (data || []).map(record => ({
      sessionId: record.session_id,
      userId: record.user_id,
      mode: record.mode,
      maxItems: record.max_items,
      targetSubjects: record.target_subjects,
      showSolution: record.show_solution,
      timeLimit: record.time_limit,
      startTime: record.start_time,
      currentAbility: record.current_ability,
      answeredItems: record.answered_items,
      responses: record.responses as TestResponse[],
      recentPerformance: record.recent_performance,
      subjectQuestionCount: record.subject_question_count as Record<string, number>,
      status: record.status,
      completedAt: record.completed_at
    }));
  } catch (error) {
    console.error('Error fetching user sessions:', error);
    return [];
  }
}