// app/auth/callback/page.tsx
'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Loader2 } from 'lucide-react'

export default function AuthCallback() {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let mounted = true
    let timeoutId: NodeJS.Timeout

    const handleAuthCallback = async () => {
      try {
        console.log('Auth callback page loaded, URL:', window.location.href)

        // Wait for Supabase to automatically process the auth tokens from URL
        // The detectSessionInUrl: true option should handle this automatically
        await new Promise(resolve => setTimeout(resolve, 1000))

        if (!mounted) return

        // Check if we now have a session
        const { data, error } = await supabase.auth.getSession()

        if (!mounted) return

        console.log('Session check:', {
          hasSession: !!data.session,
          error: error?.message,
          user: data.session?.user?.email
        })

        if (error) {
          console.error('Auth session error:', error)
          setError(`Authentication failed: ${error.message}`)
          timeoutId = setTimeout(() => {
            if (mounted) router.push('/?error=auth-failed')
          }, 2000)
          return
        }

        if (data.session) {
          console.log('Authentication successful, redirecting...')
          // Clean up the URL
          window.history.replaceState({}, document.title, window.location.pathname)
          router.push('/')
        } else {
          console.log('No session found, trying one more time...')
          // Try one more time after a longer delay
          timeoutId = setTimeout(async () => {
            if (!mounted) return

            const { data: retryData, error: retryError } = await supabase.auth.getSession()

            if (!mounted) return

            if (retryData.session) {
              console.log('Session found on retry, redirecting...')
              window.history.replaceState({}, document.title, window.location.pathname)
              router.push('/')
            } else {
              console.log('Still no session, redirecting with error')
              setError('Unable to complete authentication')
              setTimeout(() => {
                if (mounted) router.push('/?error=no-session')
              }, 1000)
            }
          }, 2000)
        }
      } catch (error) {
        if (!mounted) return

        console.error('Auth callback error:', error)
        setError('Authentication error occurred')
        timeoutId = setTimeout(() => {
          if (mounted) router.push('/?error=callback-error')
        }, 2000)
      }
    }

    // Start the auth callback handling
    handleAuthCallback()

    return () => {
      mounted = false
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        {!error && <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />}
        <h2 className="text-lg font-semibold mb-2">
          {error ? 'Authentication Error' : 'Completing sign in...'}
        </h2>
        <p className="text-muted-foreground">
          {error || 'Please wait while we finish signing you in.'}
        </p>
        {error && (
          <p className="text-sm text-muted-foreground mt-2">
            Redirecting you back to the home page...
          </p>
        )}
      </div>
    </div>
  )
}