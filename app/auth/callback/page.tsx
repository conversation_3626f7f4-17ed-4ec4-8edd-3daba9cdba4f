// app/auth/callback/page.tsx
'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Loader2 } from 'lucide-react'

export default function AuthCallback() {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let mounted = true
    let timeoutId: NodeJS.Timeout

    const handleAuthCallback = async () => {
      try {
        console.log('Auth callback started, URL:', window.location.href)

        // Parse URL for auth parameters
        const urlParams = new URLSearchParams(window.location.search)
        const hashParams = new URLSearchParams(window.location.hash.substring(1))

        const code = urlParams.get('code')
        const accessToken = hashParams.get('access_token')
        const refreshToken = hashParams.get('refresh_token')
        const error = urlParams.get('error') || hashParams.get('error')

        console.log('Auth params:', {
          hasCode: !!code,
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          error
        })

        // Set a timeout to prevent infinite loading
        const maxTimeout = setTimeout(() => {
          if (mounted) {
            console.warn('Auth callback timed out')
            setError('Authentication timed out. Please try again.')
            setTimeout(() => router.push('/?error=timeout'), 2000)
          }
        }, 15000)

        if (error) {
          console.error('OAuth error:', error)
          clearTimeout(maxTimeout)
          setError(`Authentication failed: ${error}`)
          setTimeout(() => router.push('/?error=oauth-error'), 2000)
          return
        }

        // Handle PKCE flow (authorization code)
        if (code) {
          console.log('Processing authorization code...')
          const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)

          if (exchangeError) {
            console.error('Code exchange error:', exchangeError)
            clearTimeout(maxTimeout)
            setError('Failed to exchange authorization code')
            setTimeout(() => router.push('/?error=code-exchange-failed'), 2000)
            return
          }

          if (data.session) {
            console.log('Code exchange successful!')
            clearTimeout(maxTimeout)
            window.history.replaceState({}, document.title, window.location.pathname)
            router.push('/')
            return
          }
        }

        // Handle implicit flow (access token in hash)
        if (accessToken) {
          console.log('Processing access token from hash...')
          const { data, error: setSessionError } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken || ''
          })

          if (setSessionError) {
            console.error('Set session error:', setSessionError)
            clearTimeout(maxTimeout)
            setError('Failed to set session from tokens')
            setTimeout(() => router.push('/?error=set-session-failed'), 2000)
            return
          }

          if (data.session) {
            console.log('Token session successful!')
            clearTimeout(maxTimeout)
            window.history.replaceState({}, document.title, window.location.pathname)
            router.push('/')
            return
          }
        }

        // Fallback: wait for Supabase auto-detection
        console.log('No explicit tokens found, waiting for Supabase auto-detection...')
        await new Promise(resolve => setTimeout(resolve, 2000))

        if (!mounted) return

        const { data, error: sessionError } = await supabase.auth.getSession()

        console.log('Session check result:', {
          hasSession: !!data.session,
          error: sessionError?.message,
          user: data.session?.user?.email
        })

        if (data.session) {
          console.log('Auto-detection successful!')
          clearTimeout(maxTimeout)
          window.history.replaceState({}, document.title, window.location.pathname)
          router.push('/')
        } else {
          console.log('No session found, final retry...')
          // One final retry
          setTimeout(async () => {
            if (!mounted) return

            const { data: retryData } = await supabase.auth.getSession()

            if (retryData.session) {
              console.log('Final retry successful!')
              clearTimeout(maxTimeout)
              window.history.replaceState({}, document.title, window.location.pathname)
              router.push('/')
            } else {
              console.log('All attempts failed')
              clearTimeout(maxTimeout)
              setError('Unable to complete authentication. Please try signing in again.')
              setTimeout(() => {
                if (mounted) router.push('/?error=auth-failed')
              }, 2000)
            }
          }, 3000)
        }

      } catch (error) {
        console.error('Auth callback error:', error)
        setError('An error occurred during authentication.')
        timeoutId = setTimeout(() => {
          if (mounted) router.push('/?error=callback-error')
        }, 2000)
      }
    }

    handleAuthCallback()

    return () => {
      mounted = false
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-6">
        {!error && <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />}
        <h2 className="text-lg font-semibold mb-2">
          {error ? 'Authentication Error' : 'Completing sign in...'}
        </h2>
        <p className="text-muted-foreground mb-4">
          {error || 'Please wait while we finish signing you in.'}
        </p>
        {error && (
          <p className="text-sm text-muted-foreground mt-2">
            Redirecting you back to the home page...
          </p>
        )}

        {/* Debug info in development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-6 p-4 bg-gray-100 rounded-lg text-left text-xs">
            <p><strong>Debug Info:</strong></p>
            <p>URL: {typeof window !== 'undefined' ? window.location.href : 'Loading...'}</p>
            <p>Has hash: {typeof window !== 'undefined' ? !!window.location.hash : 'Unknown'}</p>
            <p>Has search: {typeof window !== 'undefined' ? !!window.location.search : 'Unknown'}</p>
          </div>
        )}
      </div>
    </div>
  )
}