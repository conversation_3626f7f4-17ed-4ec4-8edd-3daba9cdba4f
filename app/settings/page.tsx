// app/settings/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Settings, 
  User, 
  Bell, 
  Shield,
  Palette,
  Clock,
  Globe,
  ArrowLeft,
  Save,
  Moon,
  Sun,
  Monitor
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useTheme } from 'next-themes'

export default function SettingsPage() {
  const { user, loading } = useAuth()
  const { theme, setTheme } = useTheme()
  const router = useRouter()
  
  // Settings state
  const [displayName, setDisplayName] = useState('')
  const [email, setEmail] = useState('')
  const [notifications, setNotifications] = useState({
    testComplete: true,
    weeklyReport: true,
    achievements: false
  })
  const [preferences, setPreferences] = useState({
    defaultMode: 'practice' as 'test' | 'practice',
    showSolutions: true,
    timeLimit: 0, // 0 = no limit
    autoSave: true
  })

  useEffect(() => {
    if (user && !loading) {
      setDisplayName(user.user_metadata?.full_name || '')
      setEmail(user.email || '')
    }
  }, [user, loading])

  // Add a timeout for the loading state
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        console.warn('Auth loading timed out, showing sign-in prompt')
        // Don't set loading to false here, let the auth context handle it
      }
    }, 8000) // 8 second timeout

    return () => clearTimeout(timeout)
  }, [loading])

  const handleSaveSettings = async () => {
    // Here you would save settings to your backend/database
    console.log('Saving settings:', {
      displayName,
      email,
      notifications,
      preferences
    })
    // For now, just show a success message
    // toast.success({ title: 'Settings saved', description: 'Your preferences have been updated' })
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-pulse flex space-x-4">
            <div className="rounded-full bg-slate-200 h-12 w-12"></div>
            <div className="flex-1 space-y-2 py-1">
              <div className="h-4 bg-slate-200 rounded w-3/4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-slate-200 rounded"></div>
                <div className="h-4 bg-slate-200 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Access Required</CardTitle>
            <CardDescription>
              You need to sign in to access settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.push('/')} className="w-full">
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="ghost" size="sm" onClick={() => router.push('/')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </div>

        <div className="max-w-4xl mx-auto space-y-6">
          {/* Page Title */}
          <div className="flex items-center gap-3">
            <Settings className="w-6 h-6" />
            <h1 className="text-3xl font-bold">Settings</h1>
          </div>

          {/* Profile Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Update your personal information and profile details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="displayName">Display Name</Label>
                  <Input
                    id="displayName"
                    value={displayName}
                    onChange={(e) => setDisplayName(e.target.value)}
                    placeholder="Your display name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Theme Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="w-5 h-5" />
                Appearance
              </CardTitle>
              <CardDescription>
                Customize the look and feel of the application
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Theme</Label>
                  <div className="flex gap-2 mt-2">
                    <Button
                      variant={theme === 'light' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('light')}
                    >
                      <Sun className="w-4 h-4 mr-2" />
                      Light
                    </Button>
                    <Button
                      variant={theme === 'dark' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('dark')}
                    >
                      <Moon className="w-4 h-4 mr-2" />
                      Dark
                    </Button>
                    <Button
                      variant={theme === 'system' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('system')}
                    >
                      <Monitor className="w-4 h-4 mr-2" />
                      System
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Test Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Test Preferences
              </CardTitle>
              <CardDescription>
                Configure your default test settings and behavior
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Default Test Mode</Label>
                  <div className="flex gap-2">
                    <Button
                      variant={preferences.defaultMode === 'practice' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPreferences({...preferences, defaultMode: 'practice'})}
                    >
                      Practice
                    </Button>
                    <Button
                      variant={preferences.defaultMode === 'test' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPreferences({...preferences, defaultMode: 'test'})}
                    >
                      Formal Test
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timeLimit">Default Time Limit (minutes)</Label>
                  <Input
                    id="timeLimit"
                    type="number"
                    min="0"
                    max="180"
                    value={preferences.timeLimit}
                    onChange={(e) => setPreferences({...preferences, timeLimit: parseInt(e.target.value) || 0})}
                    placeholder="0 = No limit"
                  />
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Show Solutions</Label>
                    <p className="text-xs text-muted-foreground">
                      Display solutions after answering questions in practice mode
                    </p>
                  </div>
                  <Button
                    variant={preferences.showSolutions ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreferences({...preferences, showSolutions: !preferences.showSolutions})}
                  >
                    {preferences.showSolutions ? 'Enabled' : 'Disabled'}
                  </Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Auto-Save Progress</Label>
                    <p className="text-xs text-muted-foreground">
                      Automatically save your progress during tests
                    </p>
                  </div>
                  <Button
                    variant={preferences.autoSave ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreferences({...preferences, autoSave: !preferences.autoSave})}
                  >
                    {preferences.autoSave ? 'Enabled' : 'Disabled'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="w-5 h-5" />
                Notifications
              </CardTitle>
              <CardDescription>
                Choose what notifications you'd like to receive
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Test Completion</Label>
                  <p className="text-xs text-muted-foreground">
                    Get notified when you complete a test
                  </p>
                </div>
                <Button
                  variant={notifications.testComplete ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setNotifications({...notifications, testComplete: !notifications.testComplete})}
                >
                  {notifications.testComplete ? 'Enabled' : 'Disabled'}
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Weekly Reports</Label>
                  <p className="text-xs text-muted-foreground">
                    Receive weekly progress summaries
                  </p>
                </div>
                <Button
                  variant={notifications.weeklyReport ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setNotifications({...notifications, weeklyReport: !notifications.weeklyReport})}
                >
                  {notifications.weeklyReport ? 'Enabled' : 'Disabled'}
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Achievements</Label>
                  <p className="text-xs text-muted-foreground">
                    Get notified about milestones and achievements
                  </p>
                </div>
                <Button
                  variant={notifications.achievements ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setNotifications({...notifications, achievements: !notifications.achievements})}
                >
                  {notifications.achievements ? 'Enabled' : 'Disabled'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Privacy & Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Privacy & Security
              </CardTitle>
              <CardDescription>
                Manage your privacy settings and account security
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Account Status</h4>
                  <Badge variant="default">Active</Badge>
                  <p className="text-xs text-muted-foreground mt-2">
                    Your account is active and secure
                  </p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Data Privacy</h4>
                  <p className="text-xs text-muted-foreground">
                    Your test data is stored securely and used only for progress tracking
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button onClick={handleSaveSettings} className="min-w-32">
              <Save className="w-4 h-4 mr-2" />
              Save Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}